# Application Configuration
nameOverride: scanconfig-service
fullnameOverride: scanconfig-service
replicaCount: 1

# Image Configuration
image:
  registry: "************.dkr.ecr.us-east-1.amazonaws.com"
  name: "veracode/sca-scanconfig-service"
  tag: "ATL-3809-acc54e9d"

imageConfig:
  pullPolicy: IfNotPresent

imagePullSecrets: []
initContainerConfig: {}

# Service Account Configuration
automountServiceAccountToken: true

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/__ENV_BRANCH__-upload-user
  name: sca-scanconfig-service

# Deployment Strategy
progressDeadlineSeconds: 180
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Container Configuration
command: "[java]"
args: [-jar, /data/sca-scanconfig-service.jar]

ports: |
  - name: https
    containerPort: 8506
    protocol: TCP
  - name: actuator
    containerPort: 8606
    protocol: TCP

# Pod Configuration
podLabels:
  admission.datadoghq.com/enabled: "true"

# Resource Configuration
resources:
  requests:
    cpu: 256m
    memory: 512Mi

# Health Checks
readinessProbe: |
  httpGet:
    path: /sca/scanconfig/actuator/health
    port: actuator
  initialDelaySeconds: 90

livenessProbe: |
  httpGet:
    path: /sca/scanconfig/actuator/health
    port: actuator
  initialDelaySeconds: 90

# Volume Configuration
volumeMounts:
  - name: sca-scanconfig-cert-mount
    mountPath: "/data/config/sca-scanconfig-service-keystore.jks"
    subPath: sca-scanconfig-service-keystore.jks
    readOnly: true
  - name: sca-scanconfig-cert-mount
    mountPath: "/data/config/truststore.jks"
    subPath: truststore.jks
    readOnly: true

volumes:
  - name: sca-scanconfig-cert-mount
    secret:
      defaultMode: 420
      secretName: sca-scanconfig-service-certs

# Scheduling Configuration
nodeSelector: {}
tolerations: []
affinity: {}

# Storage Configuration
persistence:
  enabled: false
  accessMode: ReadWriteOnce
  size: 1Gi

# Job Configuration
cronjob:
  enabled: false

job:
  enabled: false

# Service Configuration
service:
  type: ClusterIP
  targetPort: https
  ports:
    - port: 443
      name: https
      protocol: TCP
      targetPort: https

# Ingress Configuration
ingress:
  enabled: false
  className: nginx-ssl-passthrough
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    nginx.ingress.kubernetes.io/upstream-keepalive-timeout: "30"
  hosts:
  - host: scanconfig-__ENV_BRANCH__.__ENV_NAME__.veracode.io
    paths:
      - path: "/"
        pathType: "Prefix"

alb_ingress:
  enabled: false

virtualService:
  enabled: false

internalIngress: {}

# Environment Variables
env:
  LOGGING_LEVEL_DATADOG_TRACE_AGENT_COMMON_WRITER_DDAGENT_DDAGENTAPI: FATAL
  LOGGING_LEVEL_COM_DATADOG_PROFILING_UPLOADER_PROFILEUPLOADER: FATAL
  AGORA_SERVER_SSL_CLIENTS_ALLOWED: '*'
  APPLICATION_JMS_SCAN_MESSAGE_ENABLED: "true"
  APPLICATION_JMS_SCAN_MESSAGE_QUEUE: __ENV_BRANCH__-upload-scan-config
  APPLICATION_JMS_SCAN_REQUEST_ENABLED: "true"
  APPLICATION_SCAN_RESULT_URL: https://sca-scanresult-service
  APPLICATION_SCAN_RESULT_NOTIFY_ENABLED: "true"
  APPLICATION_SCHEDULED_JOB_ENABLE: "true"
  AWS_KMS_ENABLED: 'true'
  AWS_SECRETSMANAGER_ENABLED: 'false'
  ENVIRONMENT: qa
  SERVER_SSL_KEY_STORE: 'file:config/sca-scanconfig-service-keystore.jks'
  SERVER_SSL_TRUST_STORE: 'file:config/truststore.jks'
  SERVER_SSL_CLIENT_AUTH: want
  SPRING_DATASOURCE_URL: ********************************************************************************************************************************************
  VOSP_MESSAGING_AWS_ACCOUNT: "************"
  VOSP_MESSAGING_ENVIRONMENT: agora-__ENV_BRANCH__
  DD_TAGS: "env:__ENV_NAME__ stack_name:__ENV_BRANCH__"
  DD_SERVICE: "sca-scanconfig-service"
  DD_PROFILING_ENABLED: __DD_PROFILING_ENABLED__
  JAVA_TOOL_OPTIONS: "-javaagent:/datadog/dd-java-agent.jar"

valueFrom:
  - name: POD_NAMESPACE
    valueFrom:
      fieldRef:
        fieldPath: metadata.namespace

envFrom: |
  - secretRef:
      name: sca-scanconfig-service-secrets
